"""
User Management API endpoints - Admin only
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from app.core.database import get_db
from app.api.deps import get_current_user, require_admin
from app.models.user import User, UserRole
from app.models.user_action import UserAction, ActionType
from app.schemas.user import UserCreate, UserUpdate, User as UserSchema
from app.core.security import get_password_hash
from app.utils.logging import log_user_action

router = APIRouter()


@router.get("/", response_model=List[UserSchema])
def get_users(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    role: Optional[UserRole] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    request: Request = None
):
    """
    Get all users (Admin only)
    
    Args:
        skip: Number of records to skip
        limit: Number of records to return
        search: Search in name or email
        role: Filter by role
        is_active: Filter by active status
        db: Database session
        current_user: Current authenticated admin user
        request: HTTP request object
        
    Returns:
        List of users
    """
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.VIEW,
        resource_type="users",
        description="Viewed users list",
        request=request
    )
    
    query = db.query(User)
    
    # Apply filters
    if search:
        search_filter = f"%{search}%"
        query = query.filter(
            (User.first_name.ilike(search_filter)) |
            (User.last_name.ilike(search_filter)) |
            (User.email.ilike(search_filter))
        )
    
    if role:
        query = query.filter(User.role == role)
    
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    
    # Order by creation date (newest first)
    query = query.order_by(User.created_at.desc())
    
    return query.offset(skip).limit(limit).all()


@router.post("/", response_model=UserSchema)
def create_user(
    user_data: UserCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    request: Request = None
):
    """
    Create a new user (Admin only)
    
    Args:
        user_data: User creation data
        db: Database session
        current_user: Current authenticated admin user
        request: HTTP request object
        
    Returns:
        Created user
        
    Raises:
        HTTPException: If email already exists
    """
    # Check if email already exists
    existing_user = db.query(User).filter(User.email == user_data.email).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Create new user
    db_user = User(
        email=user_data.email,
        first_name=user_data.first_name,
        last_name=user_data.last_name,
        hashed_password=get_password_hash(user_data.password),
        role=user_data.role,
        is_active=user_data.is_active,
        is_verified=True  # Admin-created users are auto-verified
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.USER_CREATE,
        resource_type="user",
        resource_id=str(db_user.id),
        description=f"Created user: {db_user.email} with role {db_user.role}",
        request=request
    )
    
    return db_user


@router.get("/{user_id}", response_model=UserSchema)
def get_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    request: Request = None
):
    """
    Get user by ID (Admin only)
    
    Args:
        user_id: User ID
        db: Database session
        current_user: Current authenticated admin user
        request: HTTP request object
        
    Returns:
        User data
        
    Raises:
        HTTPException: If user not found
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.VIEW,
        resource_type="user",
        resource_id=str(user_id),
        description=f"Viewed user: {user.email}",
        request=request
    )
    
    return user


@router.put("/{user_id}", response_model=UserSchema)
def update_user(
    user_id: int,
    user_data: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    request: Request = None
):
    """
    Update user (Admin only)
    
    Args:
        user_id: User ID
        user_data: User update data
        db: Database session
        current_user: Current authenticated admin user
        request: HTTP request object
        
    Returns:
        Updated user
        
    Raises:
        HTTPException: If user not found or email already exists
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Check if email already exists (if being updated)
    if user_data.email and user_data.email != user.email:
        existing_user = db.query(User).filter(User.email == user_data.email).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
    
    # Update user fields
    update_data = user_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(user, field, value)
    
    user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(user)
    
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.USER_UPDATE,
        resource_type="user",
        resource_id=str(user_id),
        description=f"Updated user: {user.email}",
        request=request
    )
    
    return user


@router.delete("/{user_id}")
def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    request: Request = None
):
    """
    Delete user (Admin only)
    
    Args:
        user_id: User ID
        db: Database session
        current_user: Current authenticated admin user
        request: HTTP request object
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If user not found or trying to delete self
    """
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Log the action before deletion
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.USER_DELETE,
        resource_type="user",
        resource_id=str(user_id),
        description=f"Deleted user: {user.email}",
        request=request
    )
    
    db.delete(user)
    db.commit()
    
    return {"message": "User deleted successfully"}


@router.put("/{user_id}/password")
def change_user_password(
    user_id: int,
    password_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    request: Request = None
):
    """
    Change user password (Admin only)
    
    Args:
        user_id: User ID
        password_data: New password data
        db: Database session
        current_user: Current authenticated admin user
        request: HTTP request object
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If user not found or password invalid
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    new_password = password_data.get("password")
    if not new_password or len(new_password) < 6:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must be at least 6 characters long"
        )
    
    user.hashed_password = get_password_hash(new_password)
    user.updated_at = datetime.utcnow()
    db.commit()
    
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.USER_UPDATE,
        resource_type="user",
        resource_id=str(user_id),
        description=f"Changed password for user: {user.email}",
        request=request
    )
    
    return {"message": "Password changed successfully"}
