import React from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { Toaster } from "react-hot-toast";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import { ThemeProvider, useTheme } from "./contexts/ThemeContext";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import LoginForm from "./components/auth/LoginForm";
import Layout from "./components/layout/Layout";
import Dashboard from "./pages/Dashboard";
import Employeurs from "./pages/Employeurs";
import Assures from "./pages/Assures";
import Beneficiaires from "./pages/Beneficiaires";
import Settings from "./pages/Settings";
import AdminDashboard from "./pages/AdminDashboard";
import UserManagement from "./pages/UserManagement";
import ApiManagement from "./pages/ApiManagement";
import AuditLog from "./pages/AuditLog";
import { PageLoader } from "./components/ui/LoadingSpinner";

/**
 * App Router Component
 * Handles routing logic based on authentication state
 */
const AppRouter = () => {
  const { isAuthenticated, loading } = useAuth();
  const { isDark } = useTheme();

  if (loading) {
    return <PageLoader message="Initialisation de l'application..." />;
  }

  return (
    <div
      className={`min-h-screen transition-colors duration-300 ${
        isDark ? "dark bg-gray-900" : "bg-gray-50"
      }`}
    >
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route
            path="/login"
            element={
              isAuthenticated ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <LoginForm />
              )
            }
          />

          {/* Protected Routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="employeurs" element={<Employeurs />} />
            <Route path="assures" element={<Assures />} />
            <Route path="beneficiaires" element={<Beneficiaires />} />
            <Route path="settings" element={<Settings />} />

            {/* Admin Routes */}
            <Route path="admin" element={<AdminDashboard />} />
            <Route path="admin/users" element={<UserManagement />} />
            <Route path="admin/apis" element={<ApiManagement />} />
            <Route path="admin/audit-log" element={<AuditLog />} />
          </Route>

          {/* Catch all route */}
          <Route
            path="*"
            element={
              <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                  <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
                  <p className="text-xl text-gray-600 mb-8">Page non trouvée</p>
                  <button
                    onClick={() => window.history.back()}
                    className="btn-primary"
                  >
                    Retour
                  </button>
                </div>
              </div>
            }
          />
        </Routes>
      </Router>
    </div>
  );
};

/**
 * Main App Component
 * Root component with providers and global configuration
 */
function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <div className="App">
          <AppRouter />

          {/* Global Toast Notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: "#fff",
                color: "#374151",
                borderRadius: "12px",
                border: "1px solid #e5e7eb",
                boxShadow:
                  "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
              },
              success: {
                iconTheme: {
                  primary: "#10b981",
                  secondary: "#fff",
                },
              },
              error: {
                iconTheme: {
                  primary: "#ef4444",
                  secondary: "#fff",
                },
              },
            }}
          />
        </div>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
