"""
User Action Logging Model for CNSS platform
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from app.core.database import Base


class ActionType(PyEnum):
    """Enumeration for user action types"""
    LOGIN = "login"
    LOGOUT = "logout"
    VIEW = "view"
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    SEARCH = "search"
    EXPORT = "export"
    IMPORT = "import"
    API_TEST = "api_test"
    USER_CREATE = "user_create"
    USER_UPDATE = "user_update"
    USER_DELETE = "user_delete"
    SERVICE_CREATE = "service_create"
    SERVICE_UPDATE = "service_update"
    SERVICE_DELETE = "service_delete"


class UserAction(Base):
    """Model for logging user actions for audit trail"""
    
    __tablename__ = "user_actions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    action_type = Column(Enum(ActionType), nullable=False)
    resource_type = Column(String(100), nullable=False)  # e.g., 'user', 'service', 'employeur'
    resource_id = Column(String(100), nullable=True)  # ID of the affected resource
    description = Column(Text, nullable=False)
    ip_address = Column(String(45), nullable=True)  # IPv4/IPv6 address
    user_agent = Column(Text, nullable=True)
    request_data = Column(Text, nullable=True)  # JSON string of request data
    response_status = Column(String(10), nullable=True)  # HTTP status code
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationship
    user = relationship("User", back_populates="actions")
