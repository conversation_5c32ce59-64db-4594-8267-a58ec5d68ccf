import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  HomeIcon,
  BuildingOfficeIcon,
  UsersIcon,
  ShieldCheckIcon,
  CogIcon,
  XMarkIcon,
  UserGroupIcon,
  CommandLineIcon,
  ChartBarIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';

/**
 * Sidebar Navigation Component
 * Responsive sidebar with role-based navigation
 */
const Sidebar = ({ isOpen, onClose }) => {
  const { isDark } = useTheme();
  const { user } = useAuth();
  const location = useLocation();

  // Navigation items with role-based access
  const navigationItems = [
    {
      name: 'Tableau de bord',
      href: '/dashboard',
      icon: HomeIcon,
      roles: ['admin', 'agent']
    },
    {
      name: 'Employeurs',
      href: '/employeurs',
      icon: BuildingOfficeIcon,
      roles: ['admin', 'agent']
    },
    {
      name: 'Assuré<PERSON>',
      href: '/assures',
      icon: UsersIcon,
      roles: ['admin', 'agent']
    },
    {
      name: 'Bénéficiaires',
      href: '/beneficiaires',
      icon: ShieldCheckIcon,
      roles: ['admin', 'agent']
    },
    // Admin-only sections
    {
      name: 'Administration',
      href: '/admin',
      icon: ChartBarIcon,
      roles: ['admin'],
      isSection: true
    },
    {
      name: 'Gestion Utilisateurs',
      href: '/admin/users',
      icon: UserGroupIcon,
      roles: ['admin']
    },
    {
      name: 'Gestion APIs',
      href: '/admin/apis',
      icon: CommandLineIcon,
      roles: ['admin']
    },
    {
      name: 'Journal d\'Audit',
      href: '/admin/audit-log',
      icon: DocumentTextIcon,
      roles: ['admin']
    },
    {
      name: 'Paramètres',
      href: '/settings',
      icon: CogIcon,
      roles: ['admin', 'agent']
    }
  ];

  // Filter navigation items based on user role
  const filteredNavigation = navigationItems.filter(item =>
    item.roles.includes(user?.role || 'agent')
  );

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 40
      }
    },
    closed: {
      x: "-100%",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 40
      }
    }
  };



  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <motion.div
        variants={sidebarVariants}
        initial="closed"
        animate={isOpen ? "open" : "closed"}
        className={`sidebar-desktop fixed inset-y-0 left-0 z-50 w-64 shadow-soft transition-colors duration-300 ${
          isDark
            ? 'bg-gray-800 border-r border-gray-700'
            : 'bg-white border-r border-gray-200'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className={`flex items-center justify-between p-6 border-b transition-colors duration-300 ${
            isDark ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <div className="flex items-center">
              <div className="w-10 h-10 bg-primary-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold">C</span>
              </div>
              <div className="ml-3">
                <h2 className={`text-lg font-semibold transition-colors duration-300 ${
                  isDark ? 'text-white' : 'text-gray-900'
                }`}>CNSS</h2>
                <p className={`text-xs transition-colors duration-300 ${
                  isDark ? 'text-gray-400' : 'text-gray-500'
                }`}>Platform</p>
              </div>
            </div>

            {/* Close button for mobile */}
            <button
              onClick={onClose}
              className={`lg:hidden p-2 rounded-lg transition-colors duration-300 ${
                isDark
                  ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
              }`}
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {filteredNavigation.map((item, index) => {
              const isActive = location.pathname === item.href ||
                             location.pathname.startsWith(item.href + '/');

              // Section header
              if (item.isSection) {
                return (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="mt-6 mb-2"
                  >
                    <h3 className={`px-4 text-xs font-semibold uppercase tracking-wider transition-colors duration-300 ${
                      isDark ? 'text-gray-400' : 'text-gray-500'
                    }`}>
                      {item.name}
                    </h3>
                  </motion.div>
                );
              }

              return (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <NavLink
                    to={item.href}
                    onClick={() => {
                      // Only close sidebar on mobile
                      if (window.innerWidth < 1024) {
                        onClose();
                      }
                    }}
                    className={({ isActive: linkActive }) => {
                      const active = isActive || linkActive;
                      return `
                        group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200
                        ${active
                          ? isDark
                            ? 'bg-primary-900/20 text-primary-400 border-r-2 border-primary-400'
                            : 'bg-primary-50 text-primary-700 border-r-2 border-primary-600'
                          : isDark
                            ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                        }
                      `;
                    }}
                  >
                    <item.icon
                      className={`mr-3 h-5 w-5 transition-colors duration-200 ${
                        isActive
                          ? isDark ? 'text-primary-400' : 'text-primary-600'
                          : isDark
                            ? 'text-gray-400 group-hover:text-white'
                            : 'text-gray-500 group-hover:text-gray-700'
                      }`}
                    />
                    {item.name}
                  </NavLink>
                </motion.div>
              );
            })}
          </nav>


        </div>
      </motion.div>
    </>
  );
};

export default Sidebar;
