"""
User Action Logging Utilities
"""
import json
from typing import Optional, Dict, Any
from fastapi import Request
from sqlalchemy.orm import Session
from app.models.user_action import UserAction, ActionType


def log_user_action(
    db: Session,
    user_id: int,
    action_type: ActionType,
    resource_type: str,
    description: str,
    resource_id: Optional[str] = None,
    request: Optional[Request] = None,
    request_data: Optional[Dict[str, Any]] = None,
    response_status: Optional[str] = None
) -> UserAction:
    """
    Log a user action for audit trail
    
    Args:
        db: Database session
        user_id: ID of the user performing the action
        action_type: Type of action performed
        resource_type: Type of resource affected
        description: Human-readable description of the action
        resource_id: ID of the affected resource (optional)
        request: HTTP request object (optional)
        request_data: Additional request data to log (optional)
        response_status: HTTP response status (optional)
        
    Returns:
        Created UserAction record
    """
    # Extract request information if available
    ip_address = None
    user_agent = None
    
    if request:
        # Get client IP address
        ip_address = request.client.host if request.client else None
        
        # Get user agent
        user_agent = request.headers.get("user-agent")
        
        # If no response status provided, assume success
        if response_status is None:
            response_status = "200"
    
    # Convert request data to JSON string if provided
    request_data_json = None
    if request_data:
        try:
            request_data_json = json.dumps(request_data, default=str)
        except (TypeError, ValueError):
            request_data_json = str(request_data)
    
    # Create action log entry
    action_log = UserAction(
        user_id=user_id,
        action_type=action_type,
        resource_type=resource_type,
        resource_id=resource_id,
        description=description,
        ip_address=ip_address,
        user_agent=user_agent,
        request_data=request_data_json,
        response_status=response_status
    )
    
    db.add(action_log)
    db.commit()
    db.refresh(action_log)
    
    return action_log


def get_user_actions(
    db: Session,
    user_id: Optional[int] = None,
    action_type: Optional[ActionType] = None,
    resource_type: Optional[str] = None,
    skip: int = 0,
    limit: int = 100
) -> list[UserAction]:
    """
    Get user actions with optional filtering
    
    Args:
        db: Database session
        user_id: Filter by user ID (optional)
        action_type: Filter by action type (optional)
        resource_type: Filter by resource type (optional)
        skip: Number of records to skip
        limit: Number of records to return
        
    Returns:
        List of UserAction records
    """
    query = db.query(UserAction)
    
    if user_id:
        query = query.filter(UserAction.user_id == user_id)
    
    if action_type:
        query = query.filter(UserAction.action_type == action_type)
    
    if resource_type:
        query = query.filter(UserAction.resource_type == resource_type)
    
    # Order by creation date (newest first)
    query = query.order_by(UserAction.created_at.desc())
    
    return query.offset(skip).limit(limit).all()


def get_user_activity_summary(
    db: Session,
    user_id: int,
    days: int = 30
) -> Dict[str, Any]:
    """
    Get user activity summary for the last N days
    
    Args:
        db: Database session
        user_id: User ID
        days: Number of days to look back
        
    Returns:
        Dictionary with activity summary
    """
    from datetime import datetime, timedelta
    from sqlalchemy import func
    
    # Calculate date range
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # Get total actions count
    total_actions = db.query(UserAction).filter(
        UserAction.user_id == user_id,
        UserAction.created_at >= start_date
    ).count()
    
    # Get actions by type
    actions_by_type = db.query(
        UserAction.action_type,
        func.count(UserAction.id).label('count')
    ).filter(
        UserAction.user_id == user_id,
        UserAction.created_at >= start_date
    ).group_by(UserAction.action_type).all()
    
    # Get actions by resource type
    actions_by_resource = db.query(
        UserAction.resource_type,
        func.count(UserAction.id).label('count')
    ).filter(
        UserAction.user_id == user_id,
        UserAction.created_at >= start_date
    ).group_by(UserAction.resource_type).all()
    
    # Get recent actions
    recent_actions = db.query(UserAction).filter(
        UserAction.user_id == user_id,
        UserAction.created_at >= start_date
    ).order_by(UserAction.created_at.desc()).limit(10).all()
    
    return {
        "total_actions": total_actions,
        "period_days": days,
        "actions_by_type": {action.action_type.value: action.count for action in actions_by_type},
        "actions_by_resource": {action.resource_type: action.count for action in actions_by_resource},
        "recent_actions": [
            {
                "id": action.id,
                "action_type": action.action_type.value,
                "resource_type": action.resource_type,
                "description": action.description,
                "created_at": action.created_at.isoformat()
            }
            for action in recent_actions
        ]
    }
