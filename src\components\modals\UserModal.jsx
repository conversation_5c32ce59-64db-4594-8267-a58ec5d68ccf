import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { useTheme } from '../../contexts/ThemeContext';

/**
 * User Modal Component
 * Modal for viewing, creating, and editing users
 */
const UserModal = ({ user, mode, onClose, onSave }) => {
  const { isDark } = useTheme();
  const [formData, setFormData] = useState({
    email: '',
    first_name: '',
    last_name: '',
    password: '',
    role: 'agent',
    is_active: true
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user && (mode === 'view' || mode === 'edit')) {
      setFormData({
        email: user.email || '',
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        password: '', // Never pre-fill password
        role: user.role || 'agent',
        is_active: user.is_active !== undefined ? user.is_active : true
      });
    } else {
      // Reset form for create mode
      setFormData({
        email: '',
        first_name: '',
        last_name: '',
        password: '',
        role: 'agent',
        is_active: true
      });
    }
    setErrors({});
  }, [user, mode]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email.trim()) {
      newErrors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    if (!formData.first_name.trim()) {
      newErrors.first_name = 'Le prénom est requis';
    }

    if (!formData.last_name.trim()) {
      newErrors.last_name = 'Le nom est requis';
    }

    if (mode === 'create' && !formData.password) {
      newErrors.password = 'Le mot de passe est requis';
    } else if (formData.password && formData.password.length < 6) {
      newErrors.password = 'Le mot de passe doit contenir au moins 6 caractères';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (mode === 'view') {
      onClose();
      return;
    }

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const submitData = { ...formData };
      
      // Don't send password if it's empty in edit mode
      if (mode === 'edit' && !submitData.password) {
        delete submitData.password;
      }

      await onSave(submitData);
    } catch (error) {
      console.error('Error saving user:', error);
    } finally {
      setLoading(false);
    }
  };

  const getModalTitle = () => {
    switch (mode) {
      case 'create':
        return 'Créer un Utilisateur';
      case 'edit':
        return 'Modifier l\'Utilisateur';
      case 'view':
        return 'Détails de l\'Utilisateur';
      default:
        return 'Utilisateur';
    }
  };

  const isReadOnly = mode === 'view';

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          {/* Background overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className={`inline-block align-bottom rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full ${
              isDark ? 'bg-gray-800' : 'bg-white'
            }`}
          >
            <form onSubmit={handleSubmit}>
              {/* Header */}
              <div className={`px-6 py-4 border-b transition-colors duration-300 ${
                isDark ? 'border-gray-700' : 'border-gray-200'
              }`}>
                <div className="flex items-center justify-between">
                  <h3 className={`text-lg font-medium transition-colors duration-300 ${
                    isDark ? 'text-white' : 'text-gray-900'
                  }`}>
                    {getModalTitle()}
                  </h3>
                  <button
                    type="button"
                    onClick={onClose}
                    className={`rounded-md p-2 transition-colors duration-300 hover:${
                      isDark ? 'bg-gray-700' : 'bg-gray-100'
                    }`}
                  >
                    <XMarkIcon className={`h-5 w-5 transition-colors duration-300 ${
                      isDark ? 'text-gray-400' : 'text-gray-500'
                    }`} />
                  </button>
                </div>
              </div>

              {/* Body */}
              <div className="px-6 py-4 space-y-4">
                {/* Email */}
                <div>
                  <label className={`block text-sm font-medium mb-1 transition-colors duration-300 ${
                    isDark ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Email *
                  </label>
                  <Input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={isReadOnly}
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && (
                    <p className="text-red-500 text-xs mt-1">{errors.email}</p>
                  )}
                </div>

                {/* First Name */}
                <div>
                  <label className={`block text-sm font-medium mb-1 transition-colors duration-300 ${
                    isDark ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Prénom *
                  </label>
                  <Input
                    type="text"
                    name="first_name"
                    value={formData.first_name}
                    onChange={handleInputChange}
                    disabled={isReadOnly}
                    className={errors.first_name ? 'border-red-500' : ''}
                  />
                  {errors.first_name && (
                    <p className="text-red-500 text-xs mt-1">{errors.first_name}</p>
                  )}
                </div>

                {/* Last Name */}
                <div>
                  <label className={`block text-sm font-medium mb-1 transition-colors duration-300 ${
                    isDark ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Nom *
                  </label>
                  <Input
                    type="text"
                    name="last_name"
                    value={formData.last_name}
                    onChange={handleInputChange}
                    disabled={isReadOnly}
                    className={errors.last_name ? 'border-red-500' : ''}
                  />
                  {errors.last_name && (
                    <p className="text-red-500 text-xs mt-1">{errors.last_name}</p>
                  )}
                </div>

                {/* Password */}
                {!isReadOnly && (
                  <div>
                    <label className={`block text-sm font-medium mb-1 transition-colors duration-300 ${
                      isDark ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Mot de passe {mode === 'create' ? '*' : '(laisser vide pour ne pas changer)'}
                    </label>
                    <Input
                      type="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      placeholder={mode === 'edit' ? 'Nouveau mot de passe...' : 'Mot de passe...'}
                      className={errors.password ? 'border-red-500' : ''}
                    />
                    {errors.password && (
                      <p className="text-red-500 text-xs mt-1">{errors.password}</p>
                    )}
                  </div>
                )}

                {/* Role */}
                <div>
                  <label className={`block text-sm font-medium mb-1 transition-colors duration-300 ${
                    isDark ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Rôle
                  </label>
                  <select
                    name="role"
                    value={formData.role}
                    onChange={handleInputChange}
                    disabled={isReadOnly}
                    className={`w-full rounded-md border px-3 py-2 transition-colors duration-300 ${
                      isDark 
                        ? 'bg-gray-700 border-gray-600 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    } ${isReadOnly ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    <option value="agent">Agent</option>
                    <option value="admin">Administrateur</option>
                  </select>
                </div>

                {/* Active Status */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_active"
                    checked={formData.is_active}
                    onChange={handleInputChange}
                    disabled={isReadOnly}
                    className={`h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500 ${
                      isReadOnly ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  />
                  <label className={`ml-2 text-sm transition-colors duration-300 ${
                    isDark ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Compte actif
                  </label>
                </div>

                {/* User Info (View Mode) */}
                {isReadOnly && user && (
                  <div className={`mt-4 p-4 rounded-md transition-colors duration-300 ${
                    isDark ? 'bg-gray-700' : 'bg-gray-50'
                  }`}>
                    <h4 className={`text-sm font-medium mb-2 transition-colors duration-300 ${
                      isDark ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Informations supplémentaires
                    </h4>
                    <div className="space-y-1 text-xs">
                      <p className={`transition-colors duration-300 ${
                        isDark ? 'text-gray-400' : 'text-gray-600'
                      }`}>
                        Créé le: {new Date(user.created_at).toLocaleDateString('fr-FR')}
                      </p>
                      {user.updated_at && (
                        <p className={`transition-colors duration-300 ${
                          isDark ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                          Modifié le: {new Date(user.updated_at).toLocaleDateString('fr-FR')}
                        </p>
                      )}
                      {user.last_login && (
                        <p className={`transition-colors duration-300 ${
                          isDark ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                          Dernière connexion: {new Date(user.last_login).toLocaleDateString('fr-FR')}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className={`px-6 py-4 border-t flex justify-end space-x-3 transition-colors duration-300 ${
                isDark ? 'border-gray-700 bg-gray-750' : 'border-gray-200 bg-gray-50'
              }`}>
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                >
                  {isReadOnly ? 'Fermer' : 'Annuler'}
                </Button>
                {!isReadOnly && (
                  <Button
                    type="submit"
                    loading={loading}
                    disabled={loading}
                  >
                    {mode === 'create' ? 'Créer' : 'Modifier'}
                  </Button>
                )}
              </div>
            </form>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  );
};

export default UserModal;
