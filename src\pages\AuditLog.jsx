import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ClockIcon,
  UserIcon,
  EyeIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { useTheme } from '../contexts/ThemeContext';
import { adminAPI } from '../services/api';
import toast from 'react-hot-toast';

/**
 * Audit Log Page - Admin Only
 * View all user actions and system events
 */
const AuditLog = () => {
  const { isDark } = useTheme();
  const [auditEntries, setAuditEntries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAction, setFilterAction] = useState('all');
  const [filterUser, setFilterUser] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalEntries, setTotalEntries] = useState(0);
  const entriesPerPage = 20;

  useEffect(() => {
    fetchAuditLog();
  }, [searchTerm, filterAction, filterUser, currentPage]);

  const fetchAuditLog = async () => {
    try {
      setLoading(true);
      
      const params = {
        skip: (currentPage - 1) * entriesPerPage,
        limit: entriesPerPage
      };
      
      if (searchTerm) params.search = searchTerm;
      if (filterAction !== 'all') params.action_type = filterAction;
      if (filterUser !== 'all') params.user_id = filterUser;
      
      const response = await adminAPI.getAuditLog(params);
      setAuditEntries(response.data.entries);
      setTotalEntries(response.data.total);
    } catch (error) {
      console.error('Error fetching audit log:', error);
      toast.error('Erreur lors du chargement du journal d\'audit');
      
      // Fallback to mock data
      setAuditEntries([
        {
          id: 1,
          user: {
            id: 1,
            name: 'Admin CNSS',
            email: '<EMAIL>',
            role: 'admin'
          },
          action_type: 'login',
          resource_type: 'auth',
          description: 'Connexion réussie',
          ip_address: '*************',
          created_at: new Date().toISOString()
        },
        {
          id: 2,
          user: {
            id: 2,
            name: 'Agent Test',
            email: '<EMAIL>',
            role: 'agent'
          },
          action_type: 'view',
          resource_type: 'services',
          description: 'Consulté la liste des services',
          ip_address: '*************',
          created_at: new Date(Date.now() - 3600000).toISOString()
        }
      ]);
      setTotalEntries(2);
    } finally {
      setLoading(false);
    }
  };

  const getActionTypeColor = (actionType) => {
    const colors = {
      login: 'text-green-600 bg-green-100',
      logout: 'text-gray-600 bg-gray-100',
      view: 'text-blue-600 bg-blue-100',
      create: 'text-green-600 bg-green-100',
      update: 'text-yellow-600 bg-yellow-100',
      delete: 'text-red-600 bg-red-100',
      user_create: 'text-purple-600 bg-purple-100',
      user_update: 'text-purple-600 bg-purple-100',
      user_delete: 'text-red-600 bg-red-100',
      service_create: 'text-indigo-600 bg-indigo-100',
      service_update: 'text-indigo-600 bg-indigo-100',
      service_delete: 'text-red-600 bg-red-100',
      api_test: 'text-cyan-600 bg-cyan-100'
    };
    return colors[actionType] || 'text-gray-600 bg-gray-100';
  };

  const getRoleBadge = (role) => {
    const colors = {
      admin: 'bg-red-100 text-red-800',
      agent: 'bg-blue-100 text-blue-800'
    };
    return colors[role] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const totalPages = Math.ceil(totalEntries / entriesPerPage);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDark ? 'bg-gray-900' : 'bg-gray-50'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-between"
          >
            <div>
              <h1 className={`text-3xl font-bold transition-colors duration-300 ${
                isDark ? 'text-white' : 'text-gray-900'
              }`}>
                Journal d'Audit
              </h1>
              <p className={`mt-2 transition-colors duration-300 ${
                isDark ? 'text-gray-300' : 'text-gray-600'
              }`}>
                Historique complet des actions utilisateurs
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`text-sm transition-colors duration-300 ${
                isDark ? 'text-gray-300' : 'text-gray-600'
              }`}>
                {totalEntries} entrée(s) au total
              </span>
            </div>
          </motion.div>
        </div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-6"
        >
          <Card className={`transition-colors duration-300 ${
            isDark ? 'bg-gray-800 border-gray-700' : 'bg-white'
          }`}>
            <Card.Content className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Rechercher..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <select
                  value={filterAction}
                  onChange={(e) => setFilterAction(e.target.value)}
                  className={`rounded-md border px-3 py-2 transition-colors duration-300 ${
                    isDark 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  <option value="all">Toutes les actions</option>
                  <option value="login">Connexion</option>
                  <option value="logout">Déconnexion</option>
                  <option value="view">Consultation</option>
                  <option value="create">Création</option>
                  <option value="update">Modification</option>
                  <option value="delete">Suppression</option>
                </select>
                <select
                  value={filterUser}
                  onChange={(e) => setFilterUser(e.target.value)}
                  className={`rounded-md border px-3 py-2 transition-colors duration-300 ${
                    isDark 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  <option value="all">Tous les utilisateurs</option>
                  <option value="admin">Administrateurs</option>
                  <option value="agent">Agents</option>
                </select>
              </div>
            </Card.Content>
          </Card>
        </motion.div>

        {/* Audit Entries */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className={`transition-colors duration-300 ${
            isDark ? 'bg-gray-800 border-gray-700' : 'bg-white'
          }`}>
            <Card.Content className="p-0">
              <div className="space-y-0">
                {auditEntries.map((entry, index) => (
                  <div
                    key={entry.id}
                    className={`p-6 border-b transition-colors duration-300 ${
                      isDark ? 'border-gray-700' : 'border-gray-200'
                    } ${index === auditEntries.length - 1 ? 'border-b-0' : ''}`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-300 ${
                            isDark ? 'bg-gray-700' : 'bg-gray-100'
                          }`}>
                            <UserIcon className="h-5 w-5 text-gray-500" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className={`font-medium transition-colors duration-300 ${
                              isDark ? 'text-white' : 'text-gray-900'
                            }`}>
                              {entry.user.name}
                            </span>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleBadge(entry.user.role)}`}>
                              {entry.user.role === 'admin' ? 'Admin' : 'Agent'}
                            </span>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActionTypeColor(entry.action_type)}`}>
                              {entry.action_type}
                            </span>
                          </div>
                          <p className={`text-sm transition-colors duration-300 ${
                            isDark ? 'text-gray-300' : 'text-gray-600'
                          }`}>
                            {entry.description}
                          </p>
                          <div className={`text-xs mt-1 space-x-4 transition-colors duration-300 ${
                            isDark ? 'text-gray-400' : 'text-gray-500'
                          }`}>
                            <span>📧 {entry.user.email}</span>
                            {entry.ip_address && <span>🌐 {entry.ip_address}</span>}
                            <span>📁 {entry.resource_type}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex-shrink-0 text-right">
                        <div className={`text-sm transition-colors duration-300 ${
                          isDark ? 'text-gray-300' : 'text-gray-600'
                        }`}>
                          <ClockIcon className="h-4 w-4 inline mr-1" />
                          {formatDate(entry.created_at)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card.Content>
          </Card>
        </motion.div>

        {/* Pagination */}
        {totalPages > 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mt-6 flex items-center justify-between"
          >
            <div className={`text-sm transition-colors duration-300 ${
              isDark ? 'text-gray-300' : 'text-gray-600'
            }`}>
              Page {currentPage} sur {totalPages}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                Précédent
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Suivant
              </Button>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default AuditLog;
