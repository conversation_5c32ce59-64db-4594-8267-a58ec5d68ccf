"""
API/Service Management endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from app.core.database import get_db
from app.api.deps import get_current_user, require_admin, require_admin_or_agent
from app.models.user import User
from app.models.service import Service, ServiceType, ServiceStatus
from app.models.user_action import ActionType
from app.schemas.service import ServiceCreate, ServiceUpdate, Service as ServiceSchema
from app.utils.logging import log_user_action

router = APIRouter()


@router.get("/", response_model=List[ServiceSchema])
def get_services(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    service_type: Optional[ServiceType] = None,
    status: Optional[ServiceStatus] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin_or_agent),
    request: Request = None
):
    """
    Get all services/APIs
    
    Args:
        skip: Number of records to skip
        limit: Number of records to return
        search: Search in name, code, or description
        service_type: Filter by service type
        status: Filter by status
        db: Database session
        current_user: Current authenticated user
        request: HTTP request object
        
    Returns:
        List of services
    """
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.VIEW,
        resource_type="services",
        description="Viewed services list",
        request=request
    )
    
    query = db.query(Service)
    
    # Apply filters
    if search:
        search_filter = f"%{search}%"
        query = query.filter(
            (Service.name.ilike(search_filter)) |
            (Service.code.ilike(search_filter)) |
            (Service.description.ilike(search_filter))
        )
    
    if service_type:
        query = query.filter(Service.service_type == service_type)
    
    if status:
        query = query.filter(Service.status == status)
    
    # Order by creation date (newest first)
    query = query.order_by(Service.created_at.desc())
    
    return query.offset(skip).limit(limit).all()


@router.post("/", response_model=ServiceSchema)
def create_service(
    service_data: ServiceCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin_or_agent),
    request: Request = None
):
    """
    Create a new service/API
    
    Args:
        service_data: Service creation data
        db: Database session
        current_user: Current authenticated user
        request: HTTP request object
        
    Returns:
        Created service
        
    Raises:
        HTTPException: If service code already exists
    """
    # Check if service code already exists
    existing_service = db.query(Service).filter(Service.code == service_data.code).first()
    if existing_service:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Service code already exists"
        )
    
    # Create new service
    db_service = Service(**service_data.dict())
    
    db.add(db_service)
    db.commit()
    db.refresh(db_service)
    
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.SERVICE_CREATE,
        resource_type="service",
        resource_id=str(db_service.id),
        description=f"Created service: {db_service.name} ({db_service.code})",
        request=request
    )
    
    return db_service


@router.get("/{service_id}", response_model=ServiceSchema)
def get_service(
    service_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin_or_agent),
    request: Request = None
):
    """
    Get service by ID
    
    Args:
        service_id: Service ID
        db: Database session
        current_user: Current authenticated user
        request: HTTP request object
        
    Returns:
        Service data
        
    Raises:
        HTTPException: If service not found
    """
    service = db.query(Service).filter(Service.id == service_id).first()
    if not service:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Service not found"
        )
    
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.VIEW,
        resource_type="service",
        resource_id=str(service_id),
        description=f"Viewed service: {service.name}",
        request=request
    )
    
    return service


@router.put("/{service_id}", response_model=ServiceSchema)
def update_service(
    service_id: int,
    service_data: ServiceUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin_or_agent),
    request: Request = None
):
    """
    Update service
    
    Args:
        service_id: Service ID
        service_data: Service update data
        db: Database session
        current_user: Current authenticated user
        request: HTTP request object
        
    Returns:
        Updated service
        
    Raises:
        HTTPException: If service not found or code already exists
    """
    service = db.query(Service).filter(Service.id == service_id).first()
    if not service:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Service not found"
        )
    
    # Check if service code already exists (if being updated)
    if service_data.code and service_data.code != service.code:
        existing_service = db.query(Service).filter(Service.code == service_data.code).first()
        if existing_service:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Service code already exists"
            )
    
    # Update service fields
    update_data = service_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(service, field, value)
    
    service.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(service)
    
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.SERVICE_UPDATE,
        resource_type="service",
        resource_id=str(service_id),
        description=f"Updated service: {service.name}",
        request=request
    )
    
    return service


@router.delete("/{service_id}")
def delete_service(
    service_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin_or_agent),
    request: Request = None
):
    """
    Delete service
    
    Args:
        service_id: Service ID
        db: Database session
        current_user: Current authenticated user
        request: HTTP request object
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If service not found
    """
    service = db.query(Service).filter(Service.id == service_id).first()
    if not service:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Service not found"
        )
    
    # Log the action before deletion
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.SERVICE_DELETE,
        resource_type="service",
        resource_id=str(service_id),
        description=f"Deleted service: {service.name}",
        request=request
    )
    
    db.delete(service)
    db.commit()
    
    return {"message": "Service deleted successfully"}


@router.post("/{service_id}/test")
def test_service(
    service_id: int,
    test_data: Optional[dict] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin_or_agent),
    request: Request = None
):
    """
    Test service/API endpoint
    
    Args:
        service_id: Service ID
        test_data: Test data to send (optional)
        db: Database session
        current_user: Current authenticated user
        request: HTTP request object
        
    Returns:
        Test result
        
    Raises:
        HTTPException: If service not found
    """
    service = db.query(Service).filter(Service.id == service_id).first()
    if not service:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Service not found"
        )
    
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.API_TEST,
        resource_type="service",
        resource_id=str(service_id),
        description=f"Tested service: {service.name}",
        request=request,
        request_data=test_data
    )
    
    # Mock test result - in real implementation, this would make actual API calls
    test_result = {
        "service_id": service_id,
        "service_name": service.name,
        "endpoint_url": service.endpoint_url,
        "http_method": service.http_method,
        "test_status": "success",
        "response_time": "45ms",
        "status_code": 200,
        "response_data": {
            "message": "Service test successful",
            "timestamp": datetime.utcnow().isoformat()
        }
    }
    
    return test_result
