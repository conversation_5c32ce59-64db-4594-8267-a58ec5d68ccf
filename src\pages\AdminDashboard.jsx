import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  UsersIcon,
  CogIcon,
  ChartBarIcon,
  ClockIcon,
  EyeIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { adminAPI } from '../services/api';
import toast from 'react-hot-toast';

/**
 * Admin Dashboard Page
 * Overview of system statistics and user activity
 */
const AdminDashboard = () => {
  const { isDark } = useTheme();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);
  const [recentActions, setRecentActions] = useState([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch system statistics
      const statsResponse = await adminAPI.getSystemStats();
      setStats(statsResponse.data);
      
      // Fetch recent user actions
      const actionsResponse = await adminAPI.getUserActions({ limit: 10 });
      setRecentActions(actionsResponse.data);
      
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Erreur lors du chargement des données');
      
      // Fallback to mock data
      setStats({
        users: { total: 12, active: 10, admins: 2, agents: 10 },
        services: { total: 8, active: 6 },
        activity: {
          total_actions_30_days: 245,
          most_active_users: [
            { name: 'Agent Test', email: '<EMAIL>', action_count: 45 },
            { name: 'Admin CNSS', email: '<EMAIL>', action_count: 32 }
          ],
          action_types: {
            view: 120,
            create: 45,
            update: 35,
            delete: 15
          }
        }
      });
      
      setRecentActions([
        {
          id: 1,
          user: { name: 'Agent Test', email: '<EMAIL>' },
          action_type: 'view',
          resource_type: 'services',
          description: 'Consulté la liste des services',
          created_at: new Date().toISOString()
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const statCards = [
    {
      title: 'Utilisateurs Total',
      value: stats?.users.total || 0,
      subtitle: `${stats?.users.active || 0} actifs`,
      icon: UsersIcon,
      color: 'primary',
      change: '+2 ce mois'
    },
    {
      title: 'Administrateurs',
      value: stats?.users.admins || 0,
      subtitle: `${stats?.users.agents || 0} agents`,
      icon: ShieldCheckIcon,
      color: 'warning',
      change: 'Stable'
    },
    {
      title: 'Services API',
      value: stats?.services.total || 0,
      subtitle: `${stats?.services.active || 0} actifs`,
      icon: CogIcon,
      color: 'success',
      change: '+1 cette semaine'
    },
    {
      title: 'Actions (30j)',
      value: stats?.activity.total_actions_30_days || 0,
      subtitle: 'Activité système',
      icon: ChartBarIcon,
      color: 'info',
      change: '+15%'
    }
  ];

  const getActionTypeColor = (actionType) => {
    const colors = {
      view: 'text-blue-600',
      create: 'text-green-600',
      update: 'text-yellow-600',
      delete: 'text-red-600',
      login: 'text-purple-600'
    };
    return colors[actionType] || 'text-gray-600';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDark ? 'bg-gray-900' : 'bg-gray-50'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`text-3xl font-bold transition-colors duration-300 ${
              isDark ? 'text-white' : 'text-gray-900'
            }`}
          >
            Tableau de Bord Administrateur
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className={`mt-2 transition-colors duration-300 ${
              isDark ? 'text-gray-300' : 'text-gray-600'
            }`}
          >
            Vue d'ensemble du système et gestion des utilisateurs
          </motion.p>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statCards.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={`transition-colors duration-300 ${
                isDark ? 'bg-gray-800 border-gray-700' : 'bg-white'
              }`}>
                <Card.Content className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`text-sm font-medium transition-colors duration-300 ${
                        isDark ? 'text-gray-300' : 'text-gray-600'
                      }`}>
                        {stat.title}
                      </p>
                      <p className={`text-2xl font-bold transition-colors duration-300 ${
                        isDark ? 'text-white' : 'text-gray-900'
                      }`}>
                        {stat.value}
                      </p>
                      <p className={`text-xs transition-colors duration-300 ${
                        isDark ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        {stat.subtitle}
                      </p>
                    </div>
                    <div className={`p-3 rounded-full bg-${stat.color}-100`}>
                      <stat.icon className={`h-6 w-6 text-${stat.color}-600`} />
                    </div>
                  </div>
                  <div className="mt-4">
                    <span className="text-sm text-green-600 font-medium">
                      {stat.change}
                    </span>
                  </div>
                </Card.Content>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className={`transition-colors duration-300 ${
              isDark ? 'bg-gray-800 border-gray-700' : 'bg-white'
            }`}>
              <Card.Header>
                <Card.Title className={`transition-colors duration-300 ${
                  isDark ? 'text-white' : 'text-gray-900'
                }`}>
                  Activité Récente
                </Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  {recentActions.map((action) => (
                    <div key={action.id} className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <ClockIcon className="h-5 w-5 text-gray-400" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className={`text-sm transition-colors duration-300 ${
                          isDark ? 'text-gray-300' : 'text-gray-900'
                        }`}>
                          <span className="font-medium">{action.user?.name}</span>
                          {' '}
                          <span className={getActionTypeColor(action.action_type)}>
                            {action.action_type}
                          </span>
                          {' '}
                          {action.description}
                        </p>
                        <p className={`text-xs transition-colors duration-300 ${
                          isDark ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                          {formatDate(action.created_at)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => window.location.href = '/admin/audit-log'}
                  >
                    <EyeIcon className="h-4 w-4 mr-2" />
                    Voir le journal complet
                  </Button>
                </div>
              </Card.Content>
            </Card>
          </motion.div>

          {/* Most Active Users */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className={`transition-colors duration-300 ${
              isDark ? 'bg-gray-800 border-gray-700' : 'bg-white'
            }`}>
              <Card.Header>
                <Card.Title className={`transition-colors duration-300 ${
                  isDark ? 'text-white' : 'text-gray-900'
                }`}>
                  Utilisateurs les Plus Actifs
                </Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  {stats?.activity.most_active_users?.map((user, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <p className={`text-sm font-medium transition-colors duration-300 ${
                          isDark ? 'text-gray-300' : 'text-gray-900'
                        }`}>
                          {user.name}
                        </p>
                        <p className={`text-xs transition-colors duration-300 ${
                          isDark ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                          {user.email}
                        </p>
                      </div>
                      <span className={`text-sm font-medium transition-colors duration-300 ${
                        isDark ? 'text-blue-400' : 'text-blue-600'
                      }`}>
                        {user.action_count} actions
                      </span>
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => window.location.href = '/admin/users'}
                  >
                    <UsersIcon className="h-4 w-4 mr-2" />
                    Gérer les utilisateurs
                  </Button>
                </div>
              </Card.Content>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
