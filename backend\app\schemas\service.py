"""
Pydantic schemas for Service model
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, validator
from app.models.service import ServiceType, ServiceStatus


class ServiceBase(BaseModel):
    """Base service schema"""
    name: str
    code: str
    description: Optional[str] = None
    service_type: ServiceType
    status: ServiceStatus = ServiceStatus.ACTIVE
    endpoint_url: Optional[str] = None
    http_method: str = "POST"
    content_type: str = "application/json"
    requires_auth: bool = True
    auth_type: Optional[str] = "bearer"
    rate_limit_per_minute: Optional[int] = 60
    rate_limit_per_hour: Optional[int] = 1000
    documentation_url: Optional[str] = None
    example_request: Optional[str] = None
    example_response: Optional[str] = None
    version: str = "1.0"
    is_latest_version: bool = True


class ServiceCreate(ServiceBase):
    """Schema for creating a service"""
    
    @validator('code')
    def validate_code(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Service code cannot be empty')
        # Convert to uppercase and remove spaces
        return v.upper().replace(' ', '_')
    
    @validator('name')
    def validate_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Service name cannot be empty')
        return v.strip()
    
    @validator('http_method')
    def validate_http_method(cls, v):
        allowed_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
        if v.upper() not in allowed_methods:
            raise ValueError(f'HTTP method must be one of: {", ".join(allowed_methods)}')
        return v.upper()


class ServiceUpdate(BaseModel):
    """Schema for updating a service"""
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    service_type: Optional[ServiceType] = None
    status: Optional[ServiceStatus] = None
    endpoint_url: Optional[str] = None
    http_method: Optional[str] = None
    content_type: Optional[str] = None
    requires_auth: Optional[bool] = None
    auth_type: Optional[str] = None
    rate_limit_per_minute: Optional[int] = None
    rate_limit_per_hour: Optional[int] = None
    documentation_url: Optional[str] = None
    example_request: Optional[str] = None
    example_response: Optional[str] = None
    version: Optional[str] = None
    is_latest_version: Optional[bool] = None
    
    @validator('code')
    def validate_code(cls, v):
        if v is not None:
            if not v or len(v.strip()) == 0:
                raise ValueError('Service code cannot be empty')
            return v.upper().replace(' ', '_')
        return v
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None:
            if not v or len(v.strip()) == 0:
                raise ValueError('Service name cannot be empty')
            return v.strip()
        return v
    
    @validator('http_method')
    def validate_http_method(cls, v):
        if v is not None:
            allowed_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
            if v.upper() not in allowed_methods:
                raise ValueError(f'HTTP method must be one of: {", ".join(allowed_methods)}')
            return v.upper()
        return v


class ServiceInDB(ServiceBase):
    """Schema for service in database"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class Service(ServiceInDB):
    """Public service schema"""
    pass


class ServiceTest(BaseModel):
    """Schema for service test request"""
    test_data: Optional[dict] = None
    timeout: Optional[int] = 30


class ServiceTestResult(BaseModel):
    """Schema for service test result"""
    service_id: int
    service_name: str
    endpoint_url: Optional[str]
    http_method: str
    test_status: str  # success, error, timeout
    response_time: Optional[str]
    status_code: Optional[int]
    response_data: Optional[dict]
    error_message: Optional[str] = None
    tested_at: datetime
