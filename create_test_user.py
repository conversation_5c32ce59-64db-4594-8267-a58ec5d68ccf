#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a test user for CNSS platform
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.security import get_password_hash
from app.core.database import get_db
from app.models.user import User, UserRole
from sqlalchemy.orm import Session

def create_test_user():
    """Create a test <NAME_EMAIL> / agent123"""
    
    # Get database session
    db = next(get_db())
    
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print("✅ User <EMAIL> already exists!")
            return
        
        # Hash the password
        hashed_password = get_password_hash("agent123")
        
        # Create new user
        new_user = User(
            email="<EMAIL>",
            first_name="Agent",
            last_name="CNS<PERSON>",
            hashed_password=hashed_password,
            role=UserRole.AGENT,
            is_active=True,
            is_verified=True
        )
        
        # Add to database
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        print("✅ Test user created successfully!")
        print(f"   Email: <EMAIL>")
        print(f"   Password: agent123")
        print(f"   Role: {new_user.role}")
        print(f"   ID: {new_user.id}")
        
    except Exception as e:
        print(f"❌ Error creating user: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_test_user()
