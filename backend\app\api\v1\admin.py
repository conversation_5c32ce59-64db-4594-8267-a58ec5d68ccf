"""
Admin-only endpoints for system management
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.core.database import get_db
from app.api.deps import require_admin
from app.models.user import User
from app.models.user_action import UserAction, ActionType
from app.schemas.user_action import UserActionResponse, UserActivitySummary
from app.utils.logging import log_user_action, get_user_actions, get_user_activity_summary

router = APIRouter()


@router.get("/user-actions", response_model=List[UserActionResponse])
def get_all_user_actions(
    skip: int = 0,
    limit: int = 100,
    user_id: Optional[int] = None,
    action_type: Optional[ActionType] = None,
    resource_type: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    request: Request = None
):
    """
    Get all user actions (Admin only)
    
    Args:
        skip: Number of records to skip
        limit: Number of records to return
        user_id: Filter by user ID
        action_type: Filter by action type
        resource_type: Filter by resource type
        start_date: Filter by start date
        end_date: Filter by end date
        db: Database session
        current_user: Current authenticated admin user
        request: HTTP request object
        
    Returns:
        List of user actions
    """
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.VIEW,
        resource_type="user_actions",
        description="Viewed all user actions",
        request=request
    )
    
    query = db.query(UserAction)
    
    # Apply filters
    if user_id:
        query = query.filter(UserAction.user_id == user_id)
    
    if action_type:
        query = query.filter(UserAction.action_type == action_type)
    
    if resource_type:
        query = query.filter(UserAction.resource_type == resource_type)
    
    if start_date:
        query = query.filter(UserAction.created_at >= start_date)
    
    if end_date:
        query = query.filter(UserAction.created_at <= end_date)
    
    # Order by creation date (newest first)
    query = query.order_by(UserAction.created_at.desc())
    
    return query.offset(skip).limit(limit).all()


@router.get("/user-actions/{user_id}", response_model=List[UserActionResponse])
def get_user_actions_by_id(
    user_id: int,
    skip: int = 0,
    limit: int = 100,
    action_type: Optional[ActionType] = None,
    resource_type: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    request: Request = None
):
    """
    Get user actions by user ID (Admin only)
    
    Args:
        user_id: User ID
        skip: Number of records to skip
        limit: Number of records to return
        action_type: Filter by action type
        resource_type: Filter by resource type
        db: Database session
        current_user: Current authenticated admin user
        request: HTTP request object
        
    Returns:
        List of user actions
        
    Raises:
        HTTPException: If user not found
    """
    # Check if user exists
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.VIEW,
        resource_type="user_actions",
        resource_id=str(user_id),
        description=f"Viewed actions for user: {user.email}",
        request=request
    )
    
    return get_user_actions(
        db=db,
        user_id=user_id,
        action_type=action_type,
        resource_type=resource_type,
        skip=skip,
        limit=limit
    )


@router.get("/user-activity/{user_id}", response_model=UserActivitySummary)
def get_user_activity_summary_endpoint(
    user_id: int,
    days: int = 30,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    request: Request = None
):
    """
    Get user activity summary (Admin only)
    
    Args:
        user_id: User ID
        days: Number of days to look back
        db: Database session
        current_user: Current authenticated admin user
        request: HTTP request object
        
    Returns:
        User activity summary
        
    Raises:
        HTTPException: If user not found
    """
    # Check if user exists
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.VIEW,
        resource_type="user_activity",
        resource_id=str(user_id),
        description=f"Viewed activity summary for user: {user.email}",
        request=request
    )
    
    return get_user_activity_summary(db=db, user_id=user_id, days=days)


@router.get("/system-stats")
def get_system_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    request: Request = None
):
    """
    Get system statistics (Admin only)
    
    Args:
        db: Database session
        current_user: Current authenticated admin user
        request: HTTP request object
        
    Returns:
        System statistics
    """
    from sqlalchemy import func
    from app.models.service import Service
    
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.VIEW,
        resource_type="system_stats",
        description="Viewed system statistics",
        request=request
    )
    
    # Get user statistics
    total_users = db.query(User).count()
    active_users = db.query(User).filter(User.is_active == True).count()
    admin_users = db.query(User).filter(User.role == "admin").count()
    agent_users = db.query(User).filter(User.role == "agent").count()
    
    # Get service statistics
    total_services = db.query(Service).count()
    active_services = db.query(Service).filter(Service.status == "active").count()
    
    # Get action statistics for last 30 days
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    recent_actions = db.query(UserAction).filter(
        UserAction.created_at >= thirty_days_ago
    ).count()
    
    # Get most active users (last 30 days)
    most_active_users = db.query(
        UserAction.user_id,
        func.count(UserAction.id).label('action_count'),
        User.first_name,
        User.last_name,
        User.email
    ).join(User).filter(
        UserAction.created_at >= thirty_days_ago
    ).group_by(
        UserAction.user_id, User.first_name, User.last_name, User.email
    ).order_by(
        func.count(UserAction.id).desc()
    ).limit(5).all()
    
    # Get action types distribution
    action_types = db.query(
        UserAction.action_type,
        func.count(UserAction.id).label('count')
    ).filter(
        UserAction.created_at >= thirty_days_ago
    ).group_by(UserAction.action_type).all()
    
    return {
        "users": {
            "total": total_users,
            "active": active_users,
            "admins": admin_users,
            "agents": agent_users
        },
        "services": {
            "total": total_services,
            "active": active_services
        },
        "activity": {
            "total_actions_30_days": recent_actions,
            "most_active_users": [
                {
                    "user_id": user.user_id,
                    "name": f"{user.first_name} {user.last_name}",
                    "email": user.email,
                    "action_count": user.action_count
                }
                for user in most_active_users
            ],
            "action_types": {
                action.action_type.value: action.count 
                for action in action_types
            }
        }
    }


@router.get("/audit-log")
def get_audit_log(
    skip: int = 0,
    limit: int = 50,
    action_type: Optional[ActionType] = None,
    user_id: Optional[int] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    request: Request = None
):
    """
    Get audit log with detailed information (Admin only)
    
    Args:
        skip: Number of records to skip
        limit: Number of records to return
        action_type: Filter by action type
        user_id: Filter by user ID
        start_date: Filter by start date
        end_date: Filter by end date
        db: Database session
        current_user: Current authenticated admin user
        request: HTTP request object
        
    Returns:
        Detailed audit log
    """
    # Log the action
    log_user_action(
        db=db,
        user_id=current_user.id,
        action_type=ActionType.VIEW,
        resource_type="audit_log",
        description="Viewed audit log",
        request=request
    )
    
    query = db.query(UserAction).join(User)
    
    # Apply filters
    if action_type:
        query = query.filter(UserAction.action_type == action_type)
    
    if user_id:
        query = query.filter(UserAction.user_id == user_id)
    
    if start_date:
        query = query.filter(UserAction.created_at >= start_date)
    
    if end_date:
        query = query.filter(UserAction.created_at <= end_date)
    
    # Order by creation date (newest first)
    query = query.order_by(UserAction.created_at.desc())
    
    actions = query.offset(skip).limit(limit).all()
    
    # Format response with user information
    audit_entries = []
    for action in actions:
        audit_entries.append({
            "id": action.id,
            "user": {
                "id": action.user.id,
                "name": f"{action.user.first_name} {action.user.last_name}",
                "email": action.user.email,
                "role": action.user.role.value
            },
            "action_type": action.action_type.value,
            "resource_type": action.resource_type,
            "resource_id": action.resource_id,
            "description": action.description,
            "ip_address": action.ip_address,
            "user_agent": action.user_agent,
            "response_status": action.response_status,
            "created_at": action.created_at.isoformat()
        })
    
    return {
        "total": query.count(),
        "entries": audit_entries
    }
