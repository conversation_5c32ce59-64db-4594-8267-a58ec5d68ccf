"""
Pydantic schemas for UserAction model
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel
from app.models.user_action import ActionType


class UserActionBase(BaseModel):
    """Base user action schema"""
    action_type: ActionType
    resource_type: str
    resource_id: Optional[str] = None
    description: str
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    request_data: Optional[str] = None
    response_status: Optional[str] = None


class UserActionCreate(UserActionBase):
    """Schema for creating a user action"""
    user_id: int


class UserActionInDB(UserActionBase):
    """Schema for user action in database"""
    id: int
    user_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True


class UserActionResponse(UserActionInDB):
    """Public user action schema with user information"""
    user: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True


class UserActivitySummary(BaseModel):
    """Schema for user activity summary"""
    total_actions: int
    period_days: int
    actions_by_type: Dict[str, int]
    actions_by_resource: Dict[str, int]
    recent_actions: List[Dict[str, Any]]


class AuditLogEntry(BaseModel):
    """Schema for audit log entry"""
    id: int
    user: Dict[str, Any]
    action_type: str
    resource_type: str
    resource_id: Optional[str]
    description: str
    ip_address: Optional[str]
    user_agent: Optional[str]
    response_status: Optional[str]
    created_at: str


class AuditLogResponse(BaseModel):
    """Schema for audit log response"""
    total: int
    entries: List[AuditLogEntry]
